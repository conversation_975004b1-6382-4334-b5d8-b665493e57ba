"use client"

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON>Header, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { TrendingUp, Truck, Briefcase, Users, Plus, Clock, MapPin, AlertTriangle } from "lucide-react"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { EquipmentLocationModal } from "@/components/ui/equipment-location-modal"
import { useEquipment } from "@/lib/hooks/useEquipment"
import { useState } from "react"

const kpiData = [
  {
    title: "Fleet Utilization",
    value: "15/20 Trucks Active",
    percentage: "75%",
    trend: "+5%",
    icon: Truck,
    color: "text-blue-600",
    bgColor: "bg-blue-50",
  },
  {
    title: "Available Equipment",
    value: "5 Units Available",
    percentage: "25%",
    trend: "-2%",
    icon: TrendingUp,
    color: "text-green-600",
    bgColor: "bg-green-50",
  },
  {
    title: "Active Jobs",
    value: "12 Jobs In Progress",
    percentage: "85%",
    trend: "+8%",
    icon: Briefcase,
    color: "text-yellow-600",
    bgColor: "bg-yellow-50",
  },
  {
    title: "Fleet Locations",
    value: "View Map",
    percentage: "Real-time",
    trend: "GPS tracking",
    icon: MapPin,
    color: "text-red-600",
    bgColor: "bg-red-50",
    clickable: true,
  },
]

const recentActivity = [
  {
    id: 1,
    type: "equipment",
    message: "Excavator CAT 320 completed maintenance check",
    time: "2 hours ago",
    location: "Shop A",
    status: "completed",
  },
  {
    id: 2,
    type: "job",
    message: "New job assigned: Highway 101 Repair",
    time: "4 hours ago",
    location: "Highway 101",
    status: "active",
  },
  {
    id: 3,
    type: "alert",
    message: "Dump Truck Ford F-750 requires inspection",
    time: "6 hours ago",
    location: "Job Site B",
    status: "warning",
  },
  {
    id: 4,
    type: "team",
    message: "Mike Johnson checked in at Job Site A",
    time: "8 hours ago",
    location: "Job Site A",
    status: "info",
  },
]

export default function DashboardPage() {
  const [isLocationModalOpen, setIsLocationModalOpen] = useState(false)
  const { equipment, loading, error } = useEquipment()

  const handleLocationClick = () => {
    setIsLocationModalOpen(true)
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <SidebarTrigger className="text-gray-600 hover:bg-gray-100" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
            <p className="text-gray-600">Welcome back, John. Here's what's happening with your fleet today.</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Create Job
          </Button>
          <Button variant="outline">
            <Plus className="h-4 w-4 mr-2" />
            Add Equipment
          </Button>
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {kpiData.map((kpi) => (
          <Card
            key={kpi.title}
            className={`hover:shadow-md transition-shadow ${
              kpi.clickable ? 'cursor-pointer hover:shadow-lg' : ''
            }`}
            onClick={kpi.clickable ? handleLocationClick : undefined}
          >
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">{kpi.title}</CardTitle>
              <div className={`p-2 rounded-lg ${kpi.bgColor}`}>
                <kpi.icon className={`h-4 w-4 ${kpi.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">{kpi.value}</div>
              <div className="flex items-center gap-2 mt-1">
                <span className={`text-sm font-medium ${kpi.color}`}>({kpi.percentage})</span>
                <Badge variant="secondary" className="text-xs">
                  {kpi.trend}
                </Badge>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Recent Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-start gap-3 p-3 rounded-lg bg-gray-50">
                  <div
                    className={`p-2 rounded-full ${
                      activity.status === "completed"
                        ? "bg-green-100"
                        : activity.status === "warning"
                          ? "bg-yellow-100"
                          : activity.status === "active"
                            ? "bg-blue-100"
                            : "bg-gray-100"
                    }`}
                  >
                    {activity.type === "equipment" && <Truck className="h-4 w-4 text-green-600" />}
                    {activity.type === "job" && <Briefcase className="h-4 w-4 text-blue-600" />}
                    {activity.type === "alert" && <AlertTriangle className="h-4 w-4 text-yellow-600" />}
                    {activity.type === "team" && <Users className="h-4 w-4 text-gray-600" />}
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">{activity.message}</p>
                    <div className="flex items-center gap-4 mt-1 text-xs text-gray-500">
                      <span>{activity.time}</span>
                      <div className="flex items-center gap-1">
                        <MapPin className="h-3 w-3" />
                        {activity.location}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Quick Stats */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Stats</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Equipment Efficiency</span>
              <span className="text-sm font-semibold text-green-600">92%</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Fuel Consumption</span>
              <span className="text-sm font-semibold text-blue-600">1,240L</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Maintenance Due</span>
              <span className="text-sm font-semibold text-yellow-600">3 Units</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Jobs Completed</span>
              <span className="text-sm font-semibold text-purple-600">47 This Month</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Location Modal */}
      <EquipmentLocationModal
        isOpen={isLocationModalOpen}
        onClose={() => setIsLocationModalOpen(false)}
        equipment={equipment}
      />
    </div>
  )
}
