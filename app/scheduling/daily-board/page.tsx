"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { MapPin, Clock, Users, Truck, CheckCircle, AlertCircle, Pause, Loader2 } from "lucide-react"
import { useEquipment } from "@/lib/hooks/useEquipment"
import { assignEquipmentToJob, canAssignEquipment } from "@/lib/utils/jobAssignment"

const jobsData = [
  {
    id: 1,
    title: "Water Main Repair",
    location: "Highway 101",
    status: "active",
    priority: "high",
    requiredPeople: 2,
    requiredEquipment: ["excavator"],
    assignedPeople: ["<PERSON>"],
    assignedEquipment: [],
    startTime: "08:00",
    estimatedDuration: "4 hours",
  },
  {
    id: 2,
    title: "Foundation Pour",
    location: "Site Alpha",
    status: "active",
    priority: "medium",
    requiredPeople: 4,
    requiredEquipment: ["crane"],
    assignedPeople: ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"],
    assignedEquipment: ["<PERSON> Liebherr 100"],
    startTime: "07:00",
    estimatedDuration: "6 hours",
  },
  {
    id: 3,
    title: "Pipe Installation",
    location: "Downtown",
    status: "completed",
    priority: "low",
    requiredPeople: 3,
    requiredEquipment: ["excavator"],
    assignedPeople: ["David Kim", "Jennifer Martinez", "Robert Brown"],
    assignedEquipment: ["Excavator CAT 320"],
    startTime: "06:00",
    estimatedDuration: "3 hours",
  },
  {
    id: 4,
    title: "Emergency Gas Leak",
    location: "Main St & 5th Ave",
    status: "pending",
    priority: "urgent",
    requiredPeople: 2,
    requiredEquipment: ["service_van"],
    assignedPeople: [],
    assignedEquipment: [],
    startTime: "ASAP",
    estimatedDuration: "2 hours",
  },
]

const availablePeople = [
  {
    id: 1,
    name: "Mike Johnson",
    role: "Equipment Operator",
    status: "available",
    location: "Depot",
    skills: ["CDL-A", "Excavator"],
  },
  {
    id: 2,
    name: "Sarah Chen",
    role: "Site Supervisor",
    status: "on-break",
    location: "Site Alpha",
    skills: ["Leadership", "Safety"],
  },
  {
    id: 3,
    name: "Carlos Rodriguez",
    role: "Crane Operator",
    status: "available",
    location: "Depot",
    skills: ["Crane", "CDL-B"],
  },
]

// Transform equipment data for daily board display
const transformEquipmentForDailyBoard = (equipment: any) => {
  return {
    id: equipment.id,
    name: equipment.name,
    type: equipment.type,
    status: equipment.status,
    location: equipment.currentLocation?.address || 'Unknown Location',
    fuel: equipment.specifications?.fuelType ? `${Math.floor(Math.random() * 40) + 60}%` : 'N/A', // Mock fuel level for now
  }
}

const getStatusColor = (status: string) => {
  switch (status) {
    case "active":
      return "bg-blue-100 text-blue-800"
    case "completed":
      return "bg-green-100 text-green-800"
    case "pending":
      return "bg-yellow-100 text-yellow-800"
    case "urgent":
      return "bg-red-100 text-red-800"
    default:
      return "bg-gray-100 text-gray-800"
  }
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case "active":
      return <AlertCircle className="h-4 w-4 text-blue-600" />
    case "completed":
      return <CheckCircle className="h-4 w-4 text-green-600" />
    case "pending":
      return <Pause className="h-4 w-4 text-yellow-600" />
    case "urgent":
      return <AlertCircle className="h-4 w-4 text-red-600" />
    default:
      return <Clock className="h-4 w-4 text-gray-600" />
  }
}

export default function DailyOperationsBoardPage() {
  const [draggedItem, setDraggedItem] = useState<any>(null)
  const [assignmentLoading, setAssignmentLoading] = useState(false)

  // Fetch live equipment data
  const { equipment: rawEquipment, loading: equipmentLoading, error: equipmentError, refetch } = useEquipment({
    organizationId: 'demo-org',
    autoRefresh: true,
    refreshInterval: 30000
  })

  // Transform and filter equipment data for daily board (only available equipment)
  const availableEquipment = rawEquipment
    .filter(eq => eq.status === 'available')
    .map(transformEquipmentForDailyBoard)

  const handleDragStart = (e: React.DragEvent, item: any, type: string) => {
    setDraggedItem({ ...item, type })
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
  }

  const handleDrop = async (e: React.DragEvent, jobId: number) => {
    e.preventDefault()
    if (draggedItem && draggedItem.type === 'equipment') {
      setAssignmentLoading(true)
      try {
        // Find the original equipment data to check assignment eligibility
        const originalEquipment = rawEquipment.find(eq => eq.id === draggedItem.id)
        if (!originalEquipment) {
          throw new Error('Equipment not found')
        }

        // Check if equipment can be assigned
        const { canAssign, reason } = canAssignEquipment(originalEquipment)
        if (!canAssign) {
          alert(`Cannot assign equipment: ${reason}`)
          return
        }

        // Assign equipment to job
        const result = await assignEquipmentToJob([draggedItem.id], jobId.toString())

        if (result.success) {
          alert(`Successfully assigned ${draggedItem.name} to job ${jobId}`)
          // Refresh equipment data to reflect the assignment
          await refetch()
        } else {
          alert(`Failed to assign equipment: ${result.message}`)
        }
      } catch (error) {
        console.error('Error assigning equipment:', error)
        alert('Failed to assign equipment. Please try again.')
      } finally {
        setAssignmentLoading(false)
      }
    }
    setDraggedItem(null)
  }

  const activeJobs = jobsData.filter((job) => job.status === "active").length
  const completedJobs = jobsData.filter((job) => job.status === "completed").length
  const pendingResources = jobsData.filter(
    (job) => job.assignedPeople.length < job.requiredPeople || job.assignedEquipment.length === 0,
  ).length

  // Show loading state for equipment
  if (equipmentLoading) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <SidebarTrigger className="text-gray-600 hover:bg-gray-100" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Daily Operations</h1>
              <p className="text-gray-600">Loading equipment data...</p>
            </div>
          </div>
        </div>
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <SidebarTrigger className="text-gray-600 hover:bg-gray-100" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Daily Operations</h1>
            <p className="text-gray-600">
              {activeJobs} Active Jobs, {completedJobs} Completed, {pendingResources} Pending Resources
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          {assignmentLoading && (
            <div className="flex items-center gap-2 text-blue-600">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span className="text-sm">Assigning equipment...</span>
            </div>
          )}
          <Button className="bg-red-600 hover:bg-red-700">CREATE EMERGENCY JOB</Button>
        </div>
      </div>

      {/* Board Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Job Sites Column */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5 text-blue-600" />
              Job Sites ({jobsData.length})
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {jobsData.map((job) => (
              <div
                key={job.id}
                className="p-4 border rounded-lg hover:shadow-md transition-shadow"
                onDragOver={handleDragOver}
                onDrop={(e) => handleDrop(e, job.id)}
              >
                <div className="flex items-start justify-between mb-2">
                  <div>
                    <h4 className="font-semibold text-gray-900">{job.title}</h4>
                    <div className="flex items-center gap-1 text-sm text-gray-600">
                      <MapPin className="h-3 w-3" />
                      {job.location}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusIcon(job.status)}
                    <Badge className={getStatusColor(job.status)}>{job.status.toUpperCase()}</Badge>
                  </div>
                </div>

                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <Clock className="h-3 w-3 text-gray-400" />
                    <span>
                      {job.startTime} • {job.estimatedDuration}
                    </span>
                  </div>

                  <div className="flex items-center gap-2">
                    <Users className="h-3 w-3 text-gray-400" />
                    <span>
                      {job.assignedPeople.length}/{job.requiredPeople} People
                    </span>
                  </div>

                  <div className="flex items-center gap-2">
                    <Truck className="h-3 w-3 text-gray-400" />
                    <span>
                      {job.assignedEquipment.length}/{job.requiredEquipment.length} Equipment
                    </span>
                  </div>
                </div>

                {job.assignedPeople.length > 0 && (
                  <div className="mt-3">
                    <p className="text-xs text-gray-500 mb-1">Assigned People:</p>
                    <div className="flex flex-wrap gap-1">
                      {job.assignedPeople.map((person) => (
                        <Badge key={person} variant="outline" className="text-xs">
                          {person}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </CardContent>
        </Card>

        {/* People Column */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5 text-green-600" />
              Available People ({availablePeople.length})
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {availablePeople.map((person) => (
              <div
                key={person.id}
                className="p-4 border rounded-lg cursor-move hover:shadow-md transition-shadow"
                draggable
                onDragStart={(e) => handleDragStart(e, person, "person")}
              >
                <div className="flex items-start justify-between mb-2">
                  <div>
                    <h4 className="font-semibold text-gray-900">{person.name}</h4>
                    <p className="text-sm text-gray-600">{person.role}</p>
                  </div>
                  <Badge
                    className={
                      person.status === "available" ? "bg-green-100 text-green-800" : "bg-yellow-100 text-yellow-800"
                    }
                  >
                    {person.status === "available" ? "Available" : "On Break"}
                  </Badge>
                </div>

                <div className="flex items-center gap-1 text-sm text-gray-600 mb-2">
                  <MapPin className="h-3 w-3" />
                  {person.location}
                </div>

                <div className="flex flex-wrap gap-1">
                  {person.skills.map((skill) => (
                    <Badge key={skill} variant="secondary" className="text-xs">
                      {skill}
                    </Badge>
                  ))}
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Equipment Column */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Truck className="h-5 w-5 text-purple-600" />
              Available Equipment ({availableEquipment.length})
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {availableEquipment.map((equipment) => (
              <div
                key={equipment.id}
                className="p-4 border rounded-lg cursor-move hover:shadow-md transition-shadow"
                draggable
                onDragStart={(e) => handleDragStart(e, equipment, "equipment")}
              >
                <div className="flex items-start justify-between mb-2">
                  <div>
                    <h4 className="font-semibold text-gray-900">{equipment.name}</h4>
                    <p className="text-sm text-gray-600 capitalize">{equipment.type.replace("_", " ")}</p>
                  </div>
                  <Badge
                    className={
                      equipment.status === "available"
                        ? "bg-green-100 text-green-800"
                        : equipment.status === "in_use"
                        ? "bg-blue-100 text-blue-800"
                        : "bg-yellow-100 text-yellow-800"
                    }
                  >
                    {equipment.status === "available"
                      ? "Available"
                      : equipment.status === "in_use"
                      ? "In Use"
                      : equipment.status.charAt(0).toUpperCase() + equipment.status.slice(1)}
                  </Badge>
                </div>

                <div className="space-y-1 text-sm text-gray-600">
                  <div className="flex items-center gap-1">
                    <MapPin className="h-3 w-3" />
                    {equipment.location}
                  </div>
                  <div className="flex items-center gap-1">
                    <span>⛽</span>
                    Fuel: {equipment.fuel}
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
