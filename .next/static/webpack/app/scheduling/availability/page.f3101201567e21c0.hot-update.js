"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/scheduling/availability/page",{

/***/ "(app-pages-browser)/./app/scheduling/availability/page.tsx":
/*!**********************************************!*\
  !*** ./app/scheduling/availability/page.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ResourceAvailabilityPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./components/ui/sidebar.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_Clock_Fuel_Loader2_Mail_MapPin_Phone_Truck_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Fuel,Loader2,Mail,MapPin,Phone,Truck,Users,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Fuel_Loader2_Mail_MapPin_Phone_Truck_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Fuel,Loader2,Mail,MapPin,Phone,Truck,Users,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Fuel_Loader2_Mail_MapPin_Phone_Truck_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Fuel,Loader2,Mail,MapPin,Phone,Truck,Users,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Fuel_Loader2_Mail_MapPin_Phone_Truck_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Fuel,Loader2,Mail,MapPin,Phone,Truck,Users,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Fuel_Loader2_Mail_MapPin_Phone_Truck_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Fuel,Loader2,Mail,MapPin,Phone,Truck,Users,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Fuel_Loader2_Mail_MapPin_Phone_Truck_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Fuel,Loader2,Mail,MapPin,Phone,Truck,Users,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Fuel_Loader2_Mail_MapPin_Phone_Truck_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Fuel,Loader2,Mail,MapPin,Phone,Truck,Users,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Fuel_Loader2_Mail_MapPin_Phone_Truck_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Fuel,Loader2,Mail,MapPin,Phone,Truck,Users,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Fuel_Loader2_Mail_MapPin_Phone_Truck_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Fuel,Loader2,Mail,MapPin,Phone,Truck,Users,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/fuel.js\");\n/* harmony import */ var _lib_hooks_useEquipment__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/hooks/useEquipment */ \"(app-pages-browser)/./lib/hooks/useEquipment.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst teamMembers = [\n    {\n        id: 1,\n        name: \"Mike Johnson\",\n        role: \"Equipment Operator\",\n        status: \"available\",\n        location: \"Main Depot\",\n        phone: \"(*************\",\n        email: \"<EMAIL>\",\n        skills: [\n            \"CDL-A\",\n            \"Excavator\",\n            \"OSHA 30\"\n        ],\n        lastUpdate: \"2 min ago\"\n    },\n    {\n        id: 2,\n        name: \"Sarah Chen\",\n        role: \"Site Supervisor\",\n        status: \"available\",\n        location: \"Downtown Office\",\n        phone: \"(*************\",\n        email: \"<EMAIL>\",\n        skills: [\n            \"Leadership\",\n            \"Safety Inspector\",\n            \"Project Management\"\n        ],\n        lastUpdate: \"5 min ago\"\n    },\n    {\n        id: 3,\n        name: \"Carlos Rodriguez\",\n        role: \"Crane Operator\",\n        status: \"on-break\",\n        location: \"Site Alpha\",\n        phone: \"(*************\",\n        email: \"<EMAIL>\",\n        skills: [\n            \"Crane Operation\",\n            \"CDL-B\",\n            \"Heavy Equipment\"\n        ],\n        lastUpdate: \"1 min ago\",\n        breakUntil: \"2:00 PM\"\n    },\n    {\n        id: 4,\n        name: \"Tom Wilson\",\n        role: \"Technician\",\n        status: \"busy\",\n        location: \"Highway 101\",\n        phone: \"(*************\",\n        email: \"<EMAIL>\",\n        skills: [\n            \"Mechanical Repair\",\n            \"Diagnostics\",\n            \"Electrical\"\n        ],\n        lastUpdate: \"30 sec ago\",\n        currentJob: \"Water Main Repair #1234\"\n    },\n    {\n        id: 5,\n        name: \"Lisa Chang\",\n        role: \"Equipment Operator\",\n        status: \"busy\",\n        location: \"Site Beta\",\n        phone: \"(*************\",\n        email: \"<EMAIL>\",\n        skills: [\n            \"Bulldozer\",\n            \"CDL-A\",\n            \"Site Preparation\"\n        ],\n        lastUpdate: \"1 min ago\",\n        currentJob: \"Foundation Prep #5678\"\n    },\n    {\n        id: 6,\n        name: \"David Kim\",\n        role: \"Maintenance Specialist\",\n        status: \"off-duty\",\n        location: \"Home\",\n        phone: \"(*************\",\n        email: \"<EMAIL>\",\n        skills: [\n            \"Hydraulics\",\n            \"Preventive Maintenance\",\n            \"Welding\"\n        ],\n        lastUpdate: \"8 hours ago\",\n        availableNext: \"Tomorrow 7:00 AM\"\n    }\n];\n// Transform equipment data for availability display\nconst transformEquipmentForAvailability = (equipment)=>{\n    var _equipment_specifications, _equipment_maintenance;\n    // Get location display\n    const getLocationDisplay = ()=>{\n        var _equipment_currentLocation;\n        if ((_equipment_currentLocation = equipment.currentLocation) === null || _equipment_currentLocation === void 0 ? void 0 : _equipment_currentLocation.address) {\n            return equipment.currentLocation.address;\n        }\n        // Fallback based on status\n        switch(equipment.status){\n            case 'maintenance':\n            case 'repair':\n                return 'Shop';\n            case 'out_of_service':\n                return 'Depot';\n            case 'available':\n                return 'Depot';\n            case 'in_use':\n                return \"Job Site \".concat(String.fromCharCode(65 + Math.floor(Math.random() * 3))) // A, B, or C\n                ;\n            default:\n                return 'Unknown';\n        }\n    };\n    // Get time ago display\n    const getTimeAgo = (timestamp)=>{\n        if (!timestamp) return 'Unknown';\n        const now = new Date();\n        const past = new Date(timestamp);\n        const diffMs = now.getTime() - past.getTime();\n        const diffMins = Math.floor(diffMs / (1000 * 60));\n        if (diffMins < 1) return 'Just now';\n        if (diffMins < 60) return \"\".concat(diffMins, \" min ago\");\n        const diffHours = Math.floor(diffMins / 60);\n        if (diffHours < 24) return \"\".concat(diffHours, \" hour\").concat(diffHours !== 1 ? 's' : '', \" ago\");\n        const diffDays = Math.floor(diffHours / 24);\n        return \"\".concat(diffDays, \" day\").concat(diffDays !== 1 ? 's' : '', \" ago\");\n    };\n    return {\n        id: equipment.id,\n        name: equipment.name || 'Unknown Equipment',\n        type: equipment.type || 'Unknown',\n        status: equipment.status || 'unknown',\n        location: getLocationDisplay(),\n        fuel: ((_equipment_specifications = equipment.specifications) === null || _equipment_specifications === void 0 ? void 0 : _equipment_specifications.fuelType) ? \"\".concat(Math.floor(Math.random() * 40) + 60, \"%\") : 'N/A',\n        lastService: ((_equipment_maintenance = equipment.maintenance) === null || _equipment_maintenance === void 0 ? void 0 : _equipment_maintenance.nextServiceDue) ? typeof equipment.maintenance.nextServiceDue === 'string' ? new Date(equipment.maintenance.nextServiceDue).toLocaleDateString() : new Date(equipment.maintenance.nextServiceDue.seconds * 1000).toLocaleDateString() : 'N/A',\n        utilization: \"\".concat(Math.floor(Math.random() * 40) + 40, \"%\"),\n        lastUpdate: getTimeAgo(equipment.updatedAt || equipment.createdAt),\n        currentJob: equipment.status === 'in_use' ? \"Job #\".concat(Math.floor(Math.random() * 9999)) : undefined,\n        availableAt: equipment.status === 'in_use' ? \"\".concat(Math.floor(Math.random() * 8) + 1, \":00 PM\") : undefined,\n        availableNext: equipment.status === 'maintenance' || equipment.status === 'repair' ? 'Tomorrow' : undefined\n    };\n};\nconst getStatusColor = (status)=>{\n    switch(status){\n        case \"available\":\n            return \"bg-green-100 text-green-800\";\n        case \"busy\":\n        case \"in_use\":\n            return \"bg-blue-100 text-blue-800\";\n        case \"on-break\":\n            return \"bg-yellow-100 text-yellow-800\";\n        case \"off-duty\":\n            return \"bg-gray-100 text-gray-800\";\n        case \"maintenance\":\n        case \"repair\":\n            return \"bg-red-100 text-red-800\";\n        case \"out_of_service\":\n            return \"bg-gray-100 text-gray-800\";\n        default:\n            return \"bg-gray-100 text-gray-800\";\n    }\n};\nconst getStatusDot = (status)=>{\n    switch(status){\n        case \"available\":\n            return \"bg-green-400\";\n        case \"busy\":\n        case \"in_use\":\n            return \"bg-blue-400\";\n        case \"on-break\":\n            return \"bg-yellow-400\";\n        case \"off-duty\":\n            return \"bg-gray-400\";\n        case \"maintenance\":\n        case \"repair\":\n            return \"bg-red-400\";\n        case \"out_of_service\":\n            return \"bg-gray-400\";\n        default:\n            return \"bg-gray-400\";\n    }\n};\nfunction ResourceAvailabilityPage() {\n    _s();\n    const [peopleFilter, setPeopleFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [equipmentFilter, setEquipmentFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [locationFilter, setLocationFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Fetch live equipment data\n    const { equipment: rawEquipment, loading: equipmentLoading, error: equipmentError } = (0,_lib_hooks_useEquipment__WEBPACK_IMPORTED_MODULE_8__.useEquipment)({\n        organizationId: 'demo-org',\n        autoRefresh: true,\n        refreshInterval: 30000\n    });\n    // Transform equipment data for this page's display format\n    const equipment = rawEquipment.map(transformEquipmentForAvailability);\n    const filteredPeople = teamMembers.filter((member)=>{\n        const matchesStatus = peopleFilter === \"all\" || member.status === peopleFilter;\n        const matchesLocation = locationFilter === \"all\" || member.location.toLowerCase().includes(locationFilter.toLowerCase());\n        const matchesSearch = member.name.toLowerCase().includes(searchTerm.toLowerCase()) || member.role.toLowerCase().includes(searchTerm.toLowerCase()) || member.skills.some((skill)=>skill.toLowerCase().includes(searchTerm.toLowerCase()));\n        return matchesStatus && matchesLocation && matchesSearch;\n    });\n    const filteredEquipment = equipment.filter((item)=>{\n        const matchesStatus = equipmentFilter === \"all\" || item.status === equipmentFilter;\n        const matchesLocation = locationFilter === \"all\" || item.location.toLowerCase().includes(locationFilter.toLowerCase());\n        const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) || item.type.toLowerCase().includes(searchTerm.toLowerCase());\n        return matchesStatus && matchesLocation && matchesSearch;\n    });\n    const availablePeople = teamMembers.filter((m)=>m.status === \"available\").length;\n    const totalPeople = teamMembers.length;\n    const availableEquipment = equipment.filter((e)=>e.status === \"available\").length;\n    const totalEquipment = equipment.length;\n    // Show loading state\n    if (equipmentLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarTrigger, {\n                                className: \"text-gray-600 hover:bg-gray-100\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"Resource Availability\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Loading equipment data...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                    lineNumber: 235,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center py-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Fuel_Loader2_Mail_MapPin_Phone_Truck_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        className: \"h-8 w-8 animate-spin text-gray-400\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n            lineNumber: 234,\n            columnNumber: 7\n        }, this);\n    }\n    // Show error state\n    if (equipmentError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarTrigger, {\n                            className: \"text-gray-600 hover:bg-gray-100\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"Resource Availability\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600\",\n                                    children: [\n                                        \"Error loading equipment data: \",\n                                        equipmentError\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                    lineNumber: 256,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                lineNumber: 255,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n            lineNumber: 254,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarTrigger, {\n                            className: \"text-gray-600 hover:bg-gray-100\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"Resource Availability\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: [\n                                        availablePeople,\n                                        \" of \",\n                                        totalPeople,\n                                        \" team members available • \",\n                                        availableEquipment,\n                                        \" of \",\n                                        totalEquipment,\n                                        \" \",\n                                        \"equipment units available\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                lineNumber: 271,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-sm font-medium text-gray-600\",\n                                        children: \"Available People\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Fuel_Loader2_Mail_MapPin_Phone_Truck_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-600\",\n                                        children: availablePeople\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-600\",\n                                        children: [\n                                            \"of \",\n                                            totalPeople,\n                                            \" total\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-sm font-medium text-gray-600\",\n                                        children: \"Available Equipment\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Fuel_Loader2_Mail_MapPin_Phone_Truck_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-600\",\n                                        children: availableEquipment\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-600\",\n                                        children: [\n                                            \"of \",\n                                            totalEquipment,\n                                            \" total\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-sm font-medium text-gray-600\",\n                                        children: \"On Break\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Fuel_Loader2_Mail_MapPin_Phone_Truck_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4 text-yellow-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-yellow-600\",\n                                        children: teamMembers.filter((m)=>m.status === \"on-break\").length\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-600\",\n                                        children: \"team members\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-sm font-medium text-gray-600\",\n                                        children: \"In Maintenance\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Fuel_Loader2_Mail_MapPin_Phone_Truck_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4 text-red-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-red-600\",\n                                        children: equipment.filter((e)=>e.status === \"maintenance\" || e.status === \"repair\").length\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-600\",\n                                        children: \"equipment units\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                        lineNumber: 321,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                lineNumber: 285,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                placeholder: \"Search by name, role, or skill...\",\n                                value: searchTerm,\n                                onChange: (e)=>setSearchTerm(e.target.value)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                value: peopleFilter,\n                                onValueChange: setPeopleFilter,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                            placeholder: \"People Status\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                value: \"all\",\n                                                children: \"All People\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                value: \"available\",\n                                                children: \"Available Only\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                value: \"busy\",\n                                                children: \"Busy\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                value: \"on-break\",\n                                                children: \"On Break\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                value: \"off-duty\",\n                                                children: \"Off Duty\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                value: equipmentFilter,\n                                onValueChange: setEquipmentFilter,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                            placeholder: \"Equipment Status\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                value: \"all\",\n                                                children: \"All Equipment\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                value: \"available\",\n                                                children: \"Available Only\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                value: \"in_use\",\n                                                children: \"In Use\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                value: \"maintenance\",\n                                                children: \"Maintenance\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                value: \"repair\",\n                                                children: \"Repair\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                value: \"out_of_service\",\n                                                children: \"Out of Service\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                value: locationFilter,\n                                onValueChange: setLocationFilter,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                            placeholder: \"All Locations\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                value: \"all\",\n                                                children: \"All Locations\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                value: \"depot\",\n                                                children: \"Depot\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                value: \"site\",\n                                                children: \"Job Sites\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                value: \"downtown\",\n                                                children: \"Downtown\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                value: \"shop\",\n                                                children: \"Shop\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                    lineNumber: 337,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                lineNumber: 336,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Fuel_Loader2_Mail_MapPin_Phone_Truck_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-5 w-5 text-green-600\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 13\n                                }, this),\n                                \"Team Members (\",\n                                filteredPeople.length,\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                            lineNumber: 388,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                        lineNumber: 387,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                            children: filteredPeople.map((member)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 border rounded-lg hover:shadow-md transition-shadow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-gray-900\",\n                                                            children: member.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: member.role\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 rounded-full \".concat(getStatusDot(member.status))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                            lineNumber: 403,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            className: getStatusColor(member.status),\n                                                            children: member.status === \"on-break\" ? \"On Break\" : member.status === \"off-duty\" ? \"Off Duty\" : member.status.charAt(0).toUpperCase() + member.status.slice(1)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                            lineNumber: 404,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                    lineNumber: 402,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 text-sm text-gray-600 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Fuel_Loader2_Mail_MapPin_Phone_Truck_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                            lineNumber: 416,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        member.location\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Fuel_Loader2_Mail_MapPin_Phone_Truck_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                            lineNumber: 420,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Updated \",\n                                                        member.lastUpdate\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 19\n                                                }, this),\n                                                member.breakUntil && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-yellow-600\",\n                                                    children: [\n                                                        \"On break until \",\n                                                        member.breakUntil\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 41\n                                                }, this),\n                                                member.currentJob && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-blue-600\",\n                                                    children: [\n                                                        \"Working: \",\n                                                        member.currentJob\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 41\n                                                }, this),\n                                                member.availableNext && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-600\",\n                                                    children: [\n                                                        \"Available: \",\n                                                        member.availableNext\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 44\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-1 mb-3\",\n                                            children: member.skills.slice(0, 3).map((skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                    variant: \"secondary\",\n                                                    className: \"text-xs\",\n                                                    children: skill\n                                                }, skill, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    size: \"sm\",\n                                                    variant: \"outline\",\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Fuel_Loader2_Mail_MapPin_Phone_Truck_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                            lineNumber: 438,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Call\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    size: \"sm\",\n                                                    variant: \"outline\",\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Fuel_Loader2_Mail_MapPin_Phone_Truck_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                            lineNumber: 442,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Message\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, member.id, true, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                            lineNumber: 394,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                        lineNumber: 393,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                lineNumber: 386,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Fuel_Loader2_Mail_MapPin_Phone_Truck_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-5 w-5 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                    lineNumber: 456,\n                                    columnNumber: 13\n                                }, this),\n                                \"Equipment (\",\n                                filteredEquipment.length,\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                            lineNumber: 455,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                        lineNumber: 454,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                            children: filteredEquipment.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 border rounded-lg hover:shadow-md transition-shadow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-gray-900\",\n                                                            children: item.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                            lineNumber: 466,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: item.type\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                            lineNumber: 467,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 rounded-full \".concat(getStatusDot(item.status))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            className: getStatusColor(item.status),\n                                                            children: item.status === \"in_use\" ? \"In Use\" : item.status === \"out_of_service\" ? \"Out of Service\" : item.status.charAt(0).toUpperCase() + item.status.slice(1)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                            lineNumber: 471,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                    lineNumber: 469,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 text-sm text-gray-600 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Fuel_Loader2_Mail_MapPin_Phone_Truck_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                            lineNumber: 483,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        item.location\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Fuel_Loader2_Mail_MapPin_Phone_Truck_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                            lineNumber: 487,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Fuel: \",\n                                                        item.fuel\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Fuel_Loader2_Mail_MapPin_Phone_Truck_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                            lineNumber: 491,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Last service: \",\n                                                        item.lastService\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Fuel_Loader2_Mail_MapPin_Phone_Truck_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                            lineNumber: 495,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Updated \",\n                                                        item.lastUpdate\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 19\n                                                }, this),\n                                                item.currentJob && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-blue-600\",\n                                                    children: [\n                                                        \"In use: \",\n                                                        item.currentJob\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                    lineNumber: 498,\n                                                    columnNumber: 39\n                                                }, this),\n                                                item.availableAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-green-600\",\n                                                    children: [\n                                                        \"Available at: \",\n                                                        item.availableAt\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 40\n                                                }, this),\n                                                item.availableNext && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-600\",\n                                                    children: [\n                                                        \"Available: \",\n                                                        item.availableNext\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 42\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: \"Utilization: \"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                            lineNumber: 505,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium \".concat(Number.parseInt(item.utilization) > 80 ? \"text-red-600\" : Number.parseInt(item.utilization) > 60 ? \"text-yellow-600\" : \"text-green-600\"),\n                                                            children: item.utilization\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                            lineNumber: 506,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                    lineNumber: 504,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    size: \"sm\",\n                                                    variant: \"outline\",\n                                                    children: \"Track\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, item.id, true, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                                    lineNumber: 463,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                            lineNumber: 461,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                        lineNumber: 460,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n                lineNumber: 453,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx\",\n        lineNumber: 269,\n        columnNumber: 5\n    }, this);\n}\n_s(ResourceAvailabilityPage, \"4WSzdhCFeB6oHI8tDNxqL19Fvmw=\", false, function() {\n    return [\n        _lib_hooks_useEquipment__WEBPACK_IMPORTED_MODULE_8__.useEquipment\n    ];\n});\n_c = ResourceAvailabilityPage;\nvar _c;\n$RefreshReg$(_c, \"ResourceAvailabilityPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/scheduling/availability/page.tsx\n"));

/***/ })

});