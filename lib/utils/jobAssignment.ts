import { Equipment, EquipmentType, EquipmentStatus } from '@/lib/types/firestore'

export interface JobRequirement {
  equipmentType: EquipmentType
  quantity: number
  required: boolean
  skills?: string[]
}

export interface AssignmentResult {
  success: boolean
  assignedEquipment: Equipment[]
  unmetRequirements: JobRequirement[]
  conflicts: string[]
  message: string
}

export interface JobAssignment {
  jobId: string
  equipmentIds: string[]
  operatorIds: string[]
  assignedAt: Date
  assignedBy: string
}

/**
 * Find available equipment that matches job requirements
 */
export function findMatchingEquipment(
  availableEquipment: Equipment[],
  requirements: JobRequirement[]
): Equipment[] {
  const matches: Equipment[] = []
  
  for (const requirement of requirements) {
    const matchingEquipment = availableEquipment.filter(equipment => 
      equipment.type === requirement.equipmentType &&
      equipment.status === 'available'
    )
    
    // Take the required quantity or all available if less than required
    const selectedEquipment = matchingEquipment.slice(0, requirement.quantity)
    matches.push(...selectedEquipment)
  }
  
  return matches
}

/**
 * Check if equipment can be assigned to a job
 */
export function canAssignEquipment(
  equipment: Equipment,
  jobLocation?: { latitude: number; longitude: number }
): { canAssign: boolean; reason?: string } {
  // Check if equipment is available
  if (equipment.status !== 'available') {
    return {
      canAssign: false,
      reason: `Equipment is currently ${equipment.status}`
    }
  }
  
  // Check if equipment needs maintenance
  if (equipment.maintenance?.nextServiceDue) {
    const nextService = typeof equipment.maintenance.nextServiceDue === 'string' 
      ? new Date(equipment.maintenance.nextServiceDue)
      : new Date(equipment.maintenance.nextServiceDue.seconds * 1000)
    
    const now = new Date()
    const daysDiff = Math.ceil((nextService.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
    
    if (daysDiff <= 0) {
      return {
        canAssign: false,
        reason: 'Equipment is due for maintenance'
      }
    }
    
    if (daysDiff <= 7) {
      return {
        canAssign: true,
        reason: `Warning: Maintenance due in ${daysDiff} days`
      }
    }
  }
  
  // Check distance if job location is provided
  if (jobLocation && equipment.currentLocation) {
    const distance = calculateDistance(
      equipment.currentLocation.latitude,
      equipment.currentLocation.longitude,
      jobLocation.latitude,
      jobLocation.longitude
    )
    
    // If equipment is more than 50 miles away, flag as potential issue
    if (distance > 50) {
      return {
        canAssign: true,
        reason: `Equipment is ${Math.round(distance)} miles away`
      }
    }
  }
  
  return { canAssign: true }
}

/**
 * Calculate distance between two coordinates in miles
 */
function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 3959 // Earth's radius in miles
  const dLat = (lat2 - lat1) * Math.PI / 180
  const dLon = (lon2 - lon1) * Math.PI / 180
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
  return R * c
}

/**
 * Assign equipment to a job
 */
export async function assignEquipmentToJob(
  equipmentIds: string[],
  jobId: string,
  organizationId: string = 'demo-org'
): Promise<AssignmentResult> {
  try {
    // Update equipment status to 'in_use'
    const updatePromises = equipmentIds.map(async (equipmentId) => {
      const response = await fetch(`/api/equipment/${equipmentId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orgId: organizationId,
          status: 'in_use',
          currentJobId: jobId,
          assignedAt: new Date().toISOString()
        })
      })
      
      if (!response.ok) {
        throw new Error(`Failed to update equipment ${equipmentId}`)
      }
      
      return response.json()
    })
    
    const results = await Promise.all(updatePromises)
    const assignedEquipment = results.map(result => result.data)
    
    // Log the assignment activity
    await logEquipmentAssignment(equipmentIds, jobId, organizationId)
    
    return {
      success: true,
      assignedEquipment,
      unmetRequirements: [],
      conflicts: [],
      message: `Successfully assigned ${equipmentIds.length} equipment unit(s) to job ${jobId}`
    }
  } catch (error) {
    console.error('Error assigning equipment to job:', error)
    return {
      success: false,
      assignedEquipment: [],
      unmetRequirements: [],
      conflicts: [error instanceof Error ? error.message : 'Unknown error'],
      message: 'Failed to assign equipment to job'
    }
  }
}

/**
 * Unassign equipment from a job
 */
export async function unassignEquipmentFromJob(
  equipmentIds: string[],
  organizationId: string = 'demo-org'
): Promise<AssignmentResult> {
  try {
    // Update equipment status back to 'available'
    const updatePromises = equipmentIds.map(async (equipmentId) => {
      const response = await fetch(`/api/equipment/${equipmentId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orgId: organizationId,
          status: 'available',
          currentJobId: null,
          unassignedAt: new Date().toISOString()
        })
      })
      
      if (!response.ok) {
        throw new Error(`Failed to update equipment ${equipmentId}`)
      }
      
      return response.json()
    })
    
    const results = await Promise.all(updatePromises)
    const unassignedEquipment = results.map(result => result.data)
    
    return {
      success: true,
      assignedEquipment: unassignedEquipment,
      unmetRequirements: [],
      conflicts: [],
      message: `Successfully unassigned ${equipmentIds.length} equipment unit(s)`
    }
  } catch (error) {
    console.error('Error unassigning equipment:', error)
    return {
      success: false,
      assignedEquipment: [],
      unmetRequirements: [],
      conflicts: [error instanceof Error ? error.message : 'Unknown error'],
      message: 'Failed to unassign equipment'
    }
  }
}

/**
 * Log equipment assignment activity
 */
async function logEquipmentAssignment(
  equipmentIds: string[],
  jobId: string,
  organizationId: string
): Promise<void> {
  try {
    // This would typically log to an activity log collection
    console.log(`Equipment assignment logged: ${equipmentIds.join(', ')} assigned to job ${jobId}`)
    
    // In a real implementation, you would save this to Firestore:
    // await db.collection('organizations').doc(organizationId).collection('activityLogs').add({
    //   type: 'equipment_assignment',
    //   entityType: 'equipment',
    //   entityIds: equipmentIds,
    //   jobId,
    //   action: 'assign',
    //   description: `Assigned ${equipmentIds.length} equipment unit(s) to job ${jobId}`,
    //   createdAt: new Date().toISOString()
    // })
  } catch (error) {
    console.error('Error logging equipment assignment:', error)
  }
}

/**
 * Get equipment assignment recommendations
 */
export function getEquipmentRecommendations(
  availableEquipment: Equipment[],
  requirements: JobRequirement[],
  jobLocation?: { latitude: number; longitude: number }
): { equipment: Equipment; score: number; reasons: string[] }[] {
  const recommendations: { equipment: Equipment; score: number; reasons: string[] }[] = []
  
  for (const equipment of availableEquipment) {
    const { canAssign, reason } = canAssignEquipment(equipment, jobLocation)
    
    if (!canAssign) continue
    
    let score = 100
    const reasons: string[] = []
    
    // Check if equipment type matches any requirement
    const matchingRequirement = requirements.find(req => req.equipmentType === equipment.type)
    if (matchingRequirement) {
      score += 50
      reasons.push(`Matches required equipment type: ${equipment.type}`)
    }
    
    // Distance scoring
    if (jobLocation && equipment.currentLocation) {
      const distance = calculateDistance(
        equipment.currentLocation.latitude,
        equipment.currentLocation.longitude,
        jobLocation.latitude,
        jobLocation.longitude
      )
      
      if (distance <= 5) {
        score += 30
        reasons.push('Very close to job site')
      } else if (distance <= 15) {
        score += 20
        reasons.push('Close to job site')
      } else if (distance <= 30) {
        score += 10
        reasons.push('Moderate distance to job site')
      } else {
        score -= 10
        reasons.push(`${Math.round(distance)} miles from job site`)
      }
    }
    
    // Maintenance scoring
    if (equipment.maintenance?.nextServiceDue) {
      const nextService = typeof equipment.maintenance.nextServiceDue === 'string' 
        ? new Date(equipment.maintenance.nextServiceDue)
        : new Date(equipment.maintenance.nextServiceDue.seconds * 1000)
      
      const now = new Date()
      const daysDiff = Math.ceil((nextService.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
      
      if (daysDiff > 30) {
        score += 20
        reasons.push('Recently serviced')
      } else if (daysDiff > 14) {
        score += 10
        reasons.push('Good maintenance status')
      } else if (daysDiff > 7) {
        score -= 5
        reasons.push('Maintenance due soon')
      }
    }
    
    if (reason) {
      reasons.push(reason)
    }
    
    recommendations.push({ equipment, score, reasons })
  }
  
  return recommendations.sort((a, b) => b.score - a.score)
}
