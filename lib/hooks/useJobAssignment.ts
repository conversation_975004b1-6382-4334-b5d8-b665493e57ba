"use client"

import { useState, useCallback } from 'react'
import { Equipment } from '@/lib/types/firestore'
import { 
  assignEquipmentToJob, 
  unassignEquipmentFromJob, 
  canAssignEquipment,
  getEquipmentRecommendations,
  JobRequirement,
  AssignmentResult
} from '@/lib/utils/jobAssignment'

interface UseJobAssignmentOptions {
  organizationId?: string
  onAssignmentComplete?: (result: AssignmentResult) => void
  onAssignmentError?: (error: string) => void
}

interface UseJobAssignmentReturn {
  assignEquipment: (equipmentIds: string[], jobId: string) => Promise<AssignmentResult>
  unassignEquipment: (equipmentIds: string[]) => Promise<AssignmentResult>
  checkAssignmentEligibility: (equipment: Equipment, jobLocation?: { latitude: number; longitude: number }) => { canAssign: boolean; reason?: string }
  getRecommendations: (availableEquipment: Equipment[], requirements: JobRequirement[], jobLocation?: { latitude: number; longitude: number }) => { equipment: Equipment; score: number; reasons: string[] }[]
  isAssigning: boolean
  isUnassigning: boolean
  lastResult: AssignmentResult | null
}

export function useJobAssignment(options: UseJobAssignmentOptions = {}): UseJobAssignmentReturn {
  const {
    organizationId = 'demo-org',
    onAssignmentComplete,
    onAssignmentError
  } = options

  const [isAssigning, setIsAssigning] = useState(false)
  const [isUnassigning, setIsUnassigning] = useState(false)
  const [lastResult, setLastResult] = useState<AssignmentResult | null>(null)

  const assignEquipment = useCallback(async (
    equipmentIds: string[], 
    jobId: string
  ): Promise<AssignmentResult> => {
    setIsAssigning(true)
    try {
      const result = await assignEquipmentToJob(equipmentIds, jobId, organizationId)
      setLastResult(result)
      
      if (result.success) {
        onAssignmentComplete?.(result)
      } else {
        onAssignmentError?.(result.message)
      }
      
      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      const errorResult: AssignmentResult = {
        success: false,
        assignedEquipment: [],
        unmetRequirements: [],
        conflicts: [errorMessage],
        message: errorMessage
      }
      
      setLastResult(errorResult)
      onAssignmentError?.(errorMessage)
      return errorResult
    } finally {
      setIsAssigning(false)
    }
  }, [organizationId, onAssignmentComplete, onAssignmentError])

  const unassignEquipment = useCallback(async (
    equipmentIds: string[]
  ): Promise<AssignmentResult> => {
    setIsUnassigning(true)
    try {
      const result = await unassignEquipmentFromJob(equipmentIds, organizationId)
      setLastResult(result)
      
      if (result.success) {
        onAssignmentComplete?.(result)
      } else {
        onAssignmentError?.(result.message)
      }
      
      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      const errorResult: AssignmentResult = {
        success: false,
        assignedEquipment: [],
        unmetRequirements: [],
        conflicts: [errorMessage],
        message: errorMessage
      }
      
      setLastResult(errorResult)
      onAssignmentError?.(errorMessage)
      return errorResult
    } finally {
      setIsUnassigning(false)
    }
  }, [organizationId, onAssignmentComplete, onAssignmentError])

  const checkAssignmentEligibility = useCallback((
    equipment: Equipment, 
    jobLocation?: { latitude: number; longitude: number }
  ) => {
    return canAssignEquipment(equipment, jobLocation)
  }, [])

  const getRecommendations = useCallback((
    availableEquipment: Equipment[],
    requirements: JobRequirement[],
    jobLocation?: { latitude: number; longitude: number }
  ) => {
    return getEquipmentRecommendations(availableEquipment, requirements, jobLocation)
  }, [])

  return {
    assignEquipment,
    unassignEquipment,
    checkAssignmentEligibility,
    getRecommendations,
    isAssigning,
    isUnassigning,
    lastResult
  }
}

// Utility hook for batch operations
export function useBatchJobAssignment(options: UseJobAssignmentOptions = {}) {
  const [operations, setOperations] = useState<{
    id: string
    type: 'assign' | 'unassign'
    equipmentIds: string[]
    jobId?: string
    status: 'pending' | 'processing' | 'completed' | 'failed'
    result?: AssignmentResult
  }[]>([])

  const addOperation = useCallback((
    type: 'assign' | 'unassign',
    equipmentIds: string[],
    jobId?: string
  ) => {
    const operation = {
      id: `${type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type,
      equipmentIds,
      jobId,
      status: 'pending' as const
    }
    
    setOperations(prev => [...prev, operation])
    return operation.id
  }, [])

  const executeOperations = useCallback(async () => {
    const pendingOps = operations.filter(op => op.status === 'pending')
    
    for (const operation of pendingOps) {
      setOperations(prev => 
        prev.map(op => 
          op.id === operation.id 
            ? { ...op, status: 'processing' }
            : op
        )
      )

      try {
        let result: AssignmentResult
        
        if (operation.type === 'assign' && operation.jobId) {
          result = await assignEquipmentToJob(
            operation.equipmentIds, 
            operation.jobId, 
            options.organizationId
          )
        } else {
          result = await unassignEquipmentFromJob(
            operation.equipmentIds, 
            options.organizationId
          )
        }

        setOperations(prev => 
          prev.map(op => 
            op.id === operation.id 
              ? { ...op, status: result.success ? 'completed' : 'failed', result }
              : op
          )
        )
      } catch (error) {
        setOperations(prev => 
          prev.map(op => 
            op.id === operation.id 
              ? { 
                  ...op, 
                  status: 'failed', 
                  result: {
                    success: false,
                    assignedEquipment: [],
                    unmetRequirements: [],
                    conflicts: [error instanceof Error ? error.message : 'Unknown error'],
                    message: 'Operation failed'
                  }
                }
              : op
          )
        )
      }
    }
  }, [operations, options.organizationId])

  const clearOperations = useCallback(() => {
    setOperations([])
  }, [])

  const removeOperation = useCallback((operationId: string) => {
    setOperations(prev => prev.filter(op => op.id !== operationId))
  }, [])

  return {
    operations,
    addOperation,
    executeOperations,
    clearOperations,
    removeOperation,
    hasOperations: operations.length > 0,
    hasPendingOperations: operations.some(op => op.status === 'pending'),
    hasProcessingOperations: operations.some(op => op.status === 'processing')
  }
}
