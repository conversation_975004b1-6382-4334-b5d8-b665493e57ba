"use client"

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Truck, MapPin, Clock, Fuel, Wrench, Loader2 } from 'lucide-react'
import { useEquipment } from '@/lib/hooks/useEquipment'
import { transformEquipmentForUI, filterEquipment, UIEquipment } from '@/lib/utils/equipment'

interface EquipmentSelectorProps {
  onSelectionChange?: (selectedEquipment: UIEquipment[]) => void
  multiSelect?: boolean
  statusFilter?: string[]
  typeFilter?: string[]
  showFilters?: boolean
  organizationId?: string
  className?: string
}

export function EquipmentSelector({
  onSelectionChange,
  multiSelect = false,
  statusFilter = ['available'],
  typeFilter = [],
  showFilters = true,
  organizationId = 'demo-org',
  className = ''
}: EquipmentSelectorProps) {
  const [selectedIds, setSelectedIds] = useState<string[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [localStatusFilter, setLocalStatusFilter] = useState('all')
  const [localTypeFilter, setLocalTypeFilter] = useState('all')

  // Fetch live equipment data
  const { equipment: rawEquipment, loading, error } = useEquipment({
    organizationId,
    autoRefresh: true,
    refreshInterval: 30000
  })

  // Transform equipment data for UI display
  const equipmentData = rawEquipment.map(transformEquipmentForUI)

  // Apply filters
  const filteredEquipment = filterEquipment(equipmentData, {
    status: localStatusFilter === 'all' ? undefined : localStatusFilter,
    type: localTypeFilter === 'all' ? undefined : localTypeFilter,
    search: searchTerm || undefined
  }).filter(eq => {
    // Apply prop-based filters
    if (statusFilter.length > 0 && !statusFilter.includes(eq.status.toLowerCase())) {
      return false
    }
    if (typeFilter.length > 0 && !typeFilter.includes(eq.type.toLowerCase())) {
      return false
    }
    return true
  })

  const handleSelection = (equipment: UIEquipment) => {
    let newSelection: string[]
    
    if (multiSelect) {
      newSelection = selectedIds.includes(equipment.id)
        ? selectedIds.filter(id => id !== equipment.id)
        : [...selectedIds, equipment.id]
    } else {
      newSelection = selectedIds.includes(equipment.id) ? [] : [equipment.id]
    }
    
    setSelectedIds(newSelection)
    
    if (onSelectionChange) {
      const selectedEquipment = filteredEquipment.filter(eq => newSelection.includes(eq.id))
      onSelectionChange(selectedEquipment)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'available':
        return 'bg-green-100 text-green-800'
      case 'in use':
        return 'bg-blue-100 text-blue-800'
      case 'maintenance':
      case 'repair':
        return 'bg-red-100 text-red-800'
      case 'out of service':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
          <span className="ml-2 text-gray-600">Loading equipment...</span>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="py-8">
          <div className="text-center text-red-600">
            Error loading equipment: {error}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Truck className="h-5 w-5 text-blue-600" />
          Select Equipment ({filteredEquipment.length} available)
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {showFilters && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Input
              placeholder="Search equipment..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Select value={localStatusFilter} onValueChange={setLocalStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="available">Available</SelectItem>
                <SelectItem value="in use">In Use</SelectItem>
                <SelectItem value="maintenance">Maintenance</SelectItem>
                <SelectItem value="repair">Repair</SelectItem>
                <SelectItem value="out of service">Out of Service</SelectItem>
              </SelectContent>
            </Select>
            <Select value={localTypeFilter} onValueChange={setLocalTypeFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="excavator">Excavator</SelectItem>
                <SelectItem value="dump truck">Dump Truck</SelectItem>
                <SelectItem value="crane">Crane</SelectItem>
                <SelectItem value="bulldozer">Bulldozer</SelectItem>
                <SelectItem value="loader">Loader</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}

        <div className="space-y-3 max-h-96 overflow-y-auto">
          {filteredEquipment.map((equipment) => (
            <div
              key={equipment.id}
              className={`p-4 border rounded-lg cursor-pointer transition-all ${
                selectedIds.includes(equipment.id)
                  ? 'border-blue-500 bg-blue-50'
                  : 'hover:shadow-md hover:border-gray-300'
              }`}
              onClick={() => handleSelection(equipment)}
            >
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-center gap-3">
                  {multiSelect && (
                    <Checkbox
                      checked={selectedIds.includes(equipment.id)}
                      onChange={() => {}} // Handled by parent click
                    />
                  )}
                  <div>
                    <h4 className="font-semibold text-gray-900">{equipment.name}</h4>
                    <p className="text-sm text-gray-600">{equipment.type}</p>
                  </div>
                </div>
                <Badge className={getStatusColor(equipment.status)}>
                  {equipment.status}
                </Badge>
              </div>

              <div className="grid grid-cols-2 gap-2 text-sm text-gray-600">
                <div className="flex items-center gap-1">
                  <MapPin className="h-3 w-3" />
                  {equipment.location}
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {equipment.lastUpdate}
                </div>
                {equipment.make && equipment.model && (
                  <div className="col-span-2 text-xs text-gray-500">
                    {equipment.make} {equipment.model} {equipment.year && `(${equipment.year})`}
                  </div>
                )}
              </div>
            </div>
          ))}
          
          {filteredEquipment.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              No equipment found matching the current filters.
            </div>
          )}
        </div>

        {selectedIds.length > 0 && (
          <div className="pt-4 border-t">
            <p className="text-sm text-gray-600 mb-2">
              Selected: {selectedIds.length} equipment unit{selectedIds.length !== 1 ? 's' : ''}
            </p>
            <div className="flex flex-wrap gap-1">
              {filteredEquipment
                .filter(eq => selectedIds.includes(eq.id))
                .map(eq => (
                  <Badge key={eq.id} variant="secondary" className="text-xs">
                    {eq.name}
                  </Badge>
                ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
