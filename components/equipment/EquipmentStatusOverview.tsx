"use client"

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Truck, CheckCircle, AlertCircle, Wrench, XCircle, Loader2 } from 'lucide-react'
import { useEquipment } from '@/lib/hooks/useEquipment'
import { transformEquipmentForUI } from '@/lib/utils/equipment'

interface EquipmentStatusOverviewProps {
  organizationId?: string
  className?: string
  showDetails?: boolean
}

export function EquipmentStatusOverview({
  organizationId = 'demo-org',
  className = '',
  showDetails = true
}: EquipmentStatusOverviewProps) {
  // Fetch live equipment data
  const { equipment: rawEquipment, loading, error } = useEquipment({
    organizationId,
    autoRefresh: true,
    refreshInterval: 30000
  })

  // Transform equipment data for UI display
  const equipmentData = rawEquipment.map(transformEquipmentForUI)

  // Calculate status counts
  const statusCounts = {
    total: equipmentData.length,
    available: equipmentData.filter(eq => eq.status.toLowerCase() === 'available').length,
    inUse: equipmentData.filter(eq => eq.status.toLowerCase() === 'in use').length,
    maintenance: equipmentData.filter(eq => 
      eq.status.toLowerCase() === 'maintenance' || eq.status.toLowerCase() === 'repair'
    ).length,
    outOfService: equipmentData.filter(eq => eq.status.toLowerCase() === 'out of service').length
  }

  const utilizationRate = statusCounts.total > 0 
    ? Math.round((statusCounts.inUse / statusCounts.total) * 100)
    : 0

  const availabilityRate = statusCounts.total > 0
    ? Math.round((statusCounts.available / statusCounts.total) * 100)
    : 0

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
          <span className="ml-2 text-gray-600">Loading equipment status...</span>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="py-8">
          <div className="text-center text-red-600">
            Error loading equipment status: {error}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!showDetails) {
    return (
      <div className={`grid grid-cols-2 md:grid-cols-4 gap-4 ${className}`}>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div>
                <div className="text-2xl font-bold text-green-600">{statusCounts.available}</div>
                <div className="text-xs text-gray-600">Available</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Truck className="h-5 w-5 text-blue-600" />
              <div>
                <div className="text-2xl font-bold text-blue-600">{statusCounts.inUse}</div>
                <div className="text-xs text-gray-600">In Use</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Wrench className="h-5 w-5 text-yellow-600" />
              <div>
                <div className="text-2xl font-bold text-yellow-600">{statusCounts.maintenance}</div>
                <div className="text-xs text-gray-600">Maintenance</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <XCircle className="h-5 w-5 text-red-600" />
              <div>
                <div className="text-2xl font-bold text-red-600">{statusCounts.outOfService}</div>
                <div className="text-xs text-gray-600">Out of Service</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Truck className="h-5 w-5 text-blue-600" />
          Equipment Status Overview
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <div className="text-3xl font-bold text-green-600">{statusCounts.available}</div>
            <div className="text-sm text-gray-600">Available</div>
            <div className="text-xs text-gray-500">
              {statusCounts.total > 0 ? `${availabilityRate}% of fleet` : 'No data'}
            </div>
          </div>

          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Truck className="h-8 w-8 text-blue-600" />
            </div>
            <div className="text-3xl font-bold text-blue-600">{statusCounts.inUse}</div>
            <div className="text-sm text-gray-600">In Use</div>
            <div className="text-xs text-gray-500">
              {statusCounts.total > 0 ? `${utilizationRate}% utilization` : 'No data'}
            </div>
          </div>

          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Wrench className="h-8 w-8 text-yellow-600" />
            </div>
            <div className="text-3xl font-bold text-yellow-600">{statusCounts.maintenance}</div>
            <div className="text-sm text-gray-600">Maintenance</div>
            <div className="text-xs text-gray-500">
              {statusCounts.total > 0 
                ? `${Math.round((statusCounts.maintenance / statusCounts.total) * 100)}% of fleet`
                : 'No data'
              }
            </div>
          </div>

          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <XCircle className="h-8 w-8 text-red-600" />
            </div>
            <div className="text-3xl font-bold text-red-600">{statusCounts.outOfService}</div>
            <div className="text-sm text-gray-600">Out of Service</div>
            <div className="text-xs text-gray-500">
              {statusCounts.total > 0 
                ? `${Math.round((statusCounts.outOfService / statusCounts.total) * 100)}% of fleet`
                : 'No data'
              }
            </div>
          </div>
        </div>

        <div className="border-t pt-4">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-4">
              <span className="text-gray-600">Total Fleet Size:</span>
              <span className="font-semibold">{statusCounts.total} units</span>
            </div>
            <div className="flex items-center gap-4">
              <span className="text-gray-600">Fleet Utilization:</span>
              <span className={`font-semibold ${
                utilizationRate >= 80 ? 'text-red-600' :
                utilizationRate >= 60 ? 'text-yellow-600' :
                'text-green-600'
              }`}>
                {utilizationRate}%
              </span>
            </div>
          </div>
        </div>

        {statusCounts.maintenance > 0 && (
          <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-yellow-600" />
              <span className="text-sm font-medium text-yellow-800">
                {statusCounts.maintenance} unit{statusCounts.maintenance !== 1 ? 's' : ''} require{statusCounts.maintenance === 1 ? 's' : ''} attention
              </span>
            </div>
          </div>
        )}

        {statusCounts.outOfService > 0 && (
          <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center gap-2">
              <XCircle className="h-4 w-4 text-red-600" />
              <span className="text-sm font-medium text-red-800">
                {statusCounts.outOfService} unit{statusCounts.outOfService !== 1 ? 's' : ''} out of service
              </span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
