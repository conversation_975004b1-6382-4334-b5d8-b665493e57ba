"use client"

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Truck, MapPin, Clock, Fuel, Wrench, Loader2, Eye, Edit, Trash2 } from 'lucide-react'
import { useEquipment } from '@/lib/hooks/useEquipment'
import { transformEquipmentForUI, filterEquipment, UIEquipment } from '@/lib/utils/equipment'

interface EquipmentListProps {
  organizationId?: string
  statusFilter?: string
  typeFilter?: string
  searchTerm?: string
  showActions?: boolean
  onView?: (equipment: UIEquipment) => void
  onEdit?: (equipment: UIEquipment) => void
  onDelete?: (equipment: UIEquipment) => void
  className?: string
  compact?: boolean
}

export function EquipmentList({
  organizationId = 'demo-org',
  statusFilter,
  typeFilter,
  searchTerm,
  showActions = false,
  onView,
  onEdit,
  onDelete,
  className = '',
  compact = false
}: EquipmentListProps) {
  // Fetch live equipment data
  const { equipment: rawEquipment, loading, error } = useEquipment({
    organizationId,
    autoRefresh: true,
    refreshInterval: 30000
  })

  // Transform equipment data for UI display
  const equipmentData = rawEquipment.map(transformEquipmentForUI)

  // Apply filters
  const filteredEquipment = filterEquipment(equipmentData, {
    status: statusFilter,
    type: typeFilter,
    search: searchTerm
  })

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'available':
        return 'bg-green-100 text-green-800'
      case 'in use':
        return 'bg-blue-100 text-blue-800'
      case 'maintenance':
      case 'repair':
        return 'bg-red-100 text-red-800'
      case 'out of service':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusDot = (status: string) => {
    switch (status.toLowerCase()) {
      case 'available':
        return 'bg-green-400'
      case 'in use':
        return 'bg-blue-400'
      case 'maintenance':
      case 'repair':
        return 'bg-red-400'
      case 'out of service':
        return 'bg-gray-400'
      default:
        return 'bg-gray-400'
    }
  }

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
          <span className="ml-2 text-gray-600">Loading equipment...</span>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="py-8">
          <div className="text-center text-red-600">
            Error loading equipment: {error}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (compact) {
    return (
      <div className={`space-y-2 ${className}`}>
        {filteredEquipment.map((equipment) => (
          <div
            key={equipment.id}
            className="flex items-center justify-between p-3 border rounded-lg hover:shadow-sm transition-shadow"
          >
            <div className="flex items-center gap-3">
              <div className={`w-2 h-2 rounded-full ${getStatusDot(equipment.status)}`}></div>
              <div>
                <h4 className="font-medium text-gray-900 text-sm">{equipment.name}</h4>
                <p className="text-xs text-gray-600">{equipment.type}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge className={`${getStatusColor(equipment.status)} text-xs`}>
                {equipment.status}
              </Badge>
              {showActions && (
                <div className="flex gap-1">
                  {onView && (
                    <Button size="sm" variant="ghost" onClick={() => onView(equipment)}>
                      <Eye className="h-3 w-3" />
                    </Button>
                  )}
                  {onEdit && (
                    <Button size="sm" variant="ghost" onClick={() => onEdit(equipment)}>
                      <Edit className="h-3 w-3" />
                    </Button>
                  )}
                  {onDelete && (
                    <Button size="sm" variant="ghost" onClick={() => onDelete(equipment)}>
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              )}
            </div>
          </div>
        ))}
        
        {filteredEquipment.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No equipment found.
          </div>
        )}
      </div>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Truck className="h-5 w-5 text-blue-600" />
          Equipment ({filteredEquipment.length})
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {filteredEquipment.map((equipment) => (
            <div
              key={equipment.id}
              className="p-4 border rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="flex items-start justify-between mb-3">
                <div>
                  <h4 className="font-semibold text-gray-900">{equipment.name}</h4>
                  <p className="text-sm text-gray-600">{equipment.type}</p>
                </div>
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${getStatusDot(equipment.status)}`}></div>
                  <Badge className={getStatusColor(equipment.status)}>
                    {equipment.status}
                  </Badge>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-2 text-sm text-gray-600 mb-3">
                <div className="flex items-center gap-1">
                  <MapPin className="h-3 w-3" />
                  {equipment.location}
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {equipment.lastUpdate}
                </div>
                {equipment.operator && (
                  <div className="flex items-center gap-1">
                    <span className="text-xs">👤</span>
                    {equipment.operator}
                  </div>
                )}
                {equipment.source && (
                  <div className="flex items-center gap-1">
                    <span className="text-xs">📡</span>
                    {equipment.source}
                  </div>
                )}
              </div>

              {(equipment.make || equipment.model || equipment.year) && (
                <div className="text-xs text-gray-500 mb-3">
                  {equipment.make} {equipment.model} {equipment.year && `(${equipment.year})`}
                  {equipment.currentHours && ` • ${equipment.currentHours} hours`}
                </div>
              )}

              {equipment.nextServiceDue && (
                <div className="text-xs text-yellow-600 mb-3">
                  Next service due: {equipment.nextServiceDue}
                </div>
              )}

              {showActions && (
                <div className="flex gap-2">
                  {onView && (
                    <Button size="sm" variant="outline" onClick={() => onView(equipment)}>
                      <Eye className="h-3 w-3 mr-1" />
                      View
                    </Button>
                  )}
                  {onEdit && (
                    <Button size="sm" variant="outline" onClick={() => onEdit(equipment)}>
                      <Edit className="h-3 w-3 mr-1" />
                      Edit
                    </Button>
                  )}
                  {onDelete && (
                    <Button size="sm" variant="outline" onClick={() => onDelete(equipment)}>
                      <Trash2 className="h-3 w-3 mr-1" />
                      Delete
                    </Button>
                  )}
                </div>
              )}
            </div>
          ))}
          
          {filteredEquipment.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              No equipment found matching the current filters.
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
